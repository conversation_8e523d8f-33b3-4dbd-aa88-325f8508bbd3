export interface ActiveHours {
  startHour: number;
  endHour: number;
  activeDays: string[]; // DayOfWeek as strings
  confidence: number;
}

export interface CommunicationPattern {
  instanceId: number;
  analyzedAt: Date;
  averageIntervalMinutes: number;
  standardDeviation: number;
  minIntervalMinutes: number;
  maxIntervalMinutes: number;
  totalCallsAnalyzed: number;
  analysisPeriodDays: number;
  activeHours?: ActiveHours;
  hasSufficientData: boolean;
  detectionLogicType: string;
  detectionLogicDescription: string;
  hasConfiguredWorkingHours: boolean;
  configuredWorkingHours?: string;
  configuredWorkingDays?: string;
}

export interface CommunicationPatternInfo {
  instanceId: number;
  instanceName: string;
  customerName: string;
  customerAbbreviation: string;
  pattern: CommunicationPattern;
  lastConnectionDate?: Date;
  status: string;
  workingHoursConfigured: boolean;
}

export interface CommunicationOutageConfig {
  analysisWindowDays: number;
  toleranceMinutes: number;
  minimumFrequencyPerHour: number;
  excludeWeekends: boolean;
  excludeHolidays: boolean;
  escalationThresholdMinutes: number;
  minimumAlertInterval: number;
  patternUpdateIntervalHours: number;
  checkIntervalMinutes: number;
  minimumCallsForAnalysis: number;
  maxAnalysisIntervalMinutes: number;
}

export interface CommunicationOutage {
  instanceId: number;
  instanceName: string;
  customerName: string;
  customerAbbreviation: string;
  timeSinceLastContact: string; // TimeSpan as string
  expectedMaxInterval: string; // TimeSpan as string
  pattern: CommunicationPattern;
  lastContactTime?: Date;
}
