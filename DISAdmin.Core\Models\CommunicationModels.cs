namespace DISAdmin.Core.Models;

/// <summary>
/// Reprezentuje komunikační vzorec instance analyzovaný z historických dat
/// </summary>
public class CommunicationPattern
{
    /// <summary>
    /// ID instance
    /// </summary>
    public int InstanceId { get; set; }

    /// <summary>
    /// Čas kdy byla analýza provedena
    /// </summary>
    public DateTime AnalyzedAt { get; set; }

    /// <summary>
    /// Průměrný interval mezi komunikacemi v minutách
    /// </summary>
    public double AverageIntervalMinutes { get; set; }

    /// <summary>
    /// Standardní odchylka intervalů
    /// </summary>
    public double StandardDeviation { get; set; }

    /// <summary>
    /// Minimální pozorovaný interval v minutách
    /// </summary>
    public double MinIntervalMinutes { get; set; }

    /// <summary>
    /// Maximální pozorovaný interval v minutách
    /// </summary>
    public double MaxIntervalMinutes { get; set; }

    /// <summary>
    /// Celkový počet API volání analyzovaných pro vytvoření vzorce
    /// </summary>
    public int TotalCallsAnalyzed { get; set; }

    /// <summary>
    /// Počet dní analyzovaných pro vytvoření vzorce
    /// </summary>
    public int AnalysisPeriodDays { get; set; }

    /// <summary>
    /// Aktivní hodiny instance na základě historických dat
    /// </summary>
    public ActiveHours? ActiveHours { get; set; }

    /// <summary>
    /// Zda má instance dostatek dat pro spolehlivou analýzu
    /// </summary>
    public bool HasSufficientData { get; set; }

    /// <summary>
    /// Typ použité logiky pro detekci aktivních hodin
    /// </summary>
    public string DetectionLogicType { get; set; } = string.Empty;

    /// <summary>
    /// Detailní popis použité logiky
    /// </summary>
    public string DetectionLogicDescription { get; set; } = string.Empty;

    /// <summary>
    /// Zda instance má nastavené pracovní hodiny
    /// </summary>
    public bool HasConfiguredWorkingHours { get; set; }

    /// <summary>
    /// Nastavené pracovní hodiny (pokud existují)
    /// </summary>
    public string? ConfiguredWorkingHours { get; set; }

    /// <summary>
    /// Aktivní dny v týdnu (pokud jsou nastavené)
    /// </summary>
    public string? ConfiguredWorkingDays { get; set; }

    /// <summary>
    /// Vypočítá očekávaný maximální interval na základě statistik
    /// </summary>
    public double GetExpectedMaxInterval(double toleranceMinutes)
    {
        // Pokud nemáme dostatek historických dat, použij pouze tolerance z pravidla
        if (TotalCallsAnalyzed < 20)
        {
            return toleranceMinutes;
        }

        // Pro instance s dostatkem dat použij statistický odhad, ale omezeně
        var statisticalEstimate = AverageIntervalMinutes + (2 * StandardDeviation);

        // Omez statistický odhad na rozumné maximum (3x tolerance)
        var maxAllowedEstimate = toleranceMinutes * 3;
        statisticalEstimate = Math.Min(statisticalEstimate, maxAllowedEstimate);

        // Použij větší z hodnot: statistický odhad nebo minimální tolerance
        return Math.Max(statisticalEstimate, toleranceMinutes);
    }
}

/// <summary>
/// Reprezentuje detekovaný výpadek komunikace
/// </summary>
public class CommunicationOutage
{
    /// <summary>
    /// ID instance s výpadkem
    /// </summary>
    public int InstanceId { get; set; }

    /// <summary>
    /// Název instance
    /// </summary>
    public string InstanceName { get; set; } = string.Empty;

    /// <summary>
    /// Název zákazníka
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// Zkratka zákazníka
    /// </summary>
    public string CustomerAbbreviation { get; set; } = string.Empty;

    /// <summary>
    /// Čas od posledního kontaktu
    /// </summary>
    public TimeSpan TimeSinceLastContact { get; set; }

    /// <summary>
    /// Očekávaný maximální interval pro tuto instanci
    /// </summary>
    public TimeSpan ExpectedMaxInterval { get; set; }

    /// <summary>
    /// Komunikační vzorec použitý pro detekci
    /// </summary>
    public CommunicationPattern Pattern { get; set; } = null!;

    /// <summary>
    /// Čas posledního kontaktu
    /// </summary>
    public DateTime? LastContactTime { get; set; }

    /// <summary>
    /// Závažnost výpadku na základě délky
    /// </summary>
    public int GetSeverity()
    {
        // Eskalace závažnosti podle délky výpadku
        if (TimeSinceLastContact.TotalMinutes > 240) // 4 hodiny
            return 3; // Critical
        if (TimeSinceLastContact.TotalMinutes > 120) // 2 hodiny
            return 2; // Warning
        return 1; // Info
    }

    /// <summary>
    /// Vytvoří popis výpadku pro alert
    /// </summary>
    public string GetDescription()
    {
        return $"Instance {InstanceName} (zákazník: {CustomerAbbreviation}) " +
               $"nekomunikuje již {TimeSinceLastContact.TotalMinutes:F0} minut. " +
               $"Očekávaný maximální interval: {ExpectedMaxInterval.TotalMinutes:F0} minut. " +
               $"Poslední kontakt: {(LastContactTime?.ToString("dd.MM.yyyy HH:mm") ?? "Neznámý")}.";
    }
}

/// <summary>
/// Konfigurace pro detekci výpadků komunikace
/// </summary>
public class CommunicationOutageConfig
{
    /// <summary>
    /// Počet dní pro analýzu komunikačních vzorců
    /// </summary>
    public int AnalysisWindowDays { get; set; } = 30;

    /// <summary>
    /// Minimální tolerance v minutách (použije se pokud statistický odhad je menší)
    /// </summary>
    public double ToleranceMinutes { get; set; } = 45;

    /// <summary>
    /// Minimální frekvence komunikace pro aktivaci monitoringu (volání za hodinu)
    /// </summary>
    public double MinimumFrequencyPerHour { get; set; } = 0.5;

    /// <summary>
    /// Zda vyloučit víkendy z analýzy a detekce
    /// </summary>
    public bool ExcludeWeekends { get; set; } = true;

    /// <summary>
    /// Zda vyloučit svátky z analýzy a detekce
    /// </summary>
    public bool ExcludeHolidays { get; set; } = true;

    /// <summary>
    /// Práh pro eskalaci závažnosti v minutách
    /// </summary>
    public int EscalationThresholdMinutes { get; set; } = 120;

    /// <summary>
    /// Minimální interval mezi alerty pro stejnou instanci v minutách
    /// </summary>
    public int MinimumAlertInterval { get; set; } = 60;

    /// <summary>
    /// Interval aktualizace komunikačních vzorců v hodinách
    /// </summary>
    public int PatternUpdateIntervalHours { get; set; } = 6;

    /// <summary>
    /// Interval kontroly výpadků v minutách
    /// </summary>
    public int CheckIntervalMinutes { get; set; } = 10;

    /// <summary>
    /// Minimální počet API volání potřebný pro spolehlivou analýzu
    /// </summary>
    public int MinimumCallsForAnalysis { get; set; } = 10;

    /// <summary>
    /// Maximální interval mezi voláními který se bere v úvahu při analýze (minuty)
    /// </summary>
    public int MaxAnalysisIntervalMinutes { get; set; } = 1440; // 24 hodin
}

/// <summary>
/// Reprezentuje aktivní hodiny instance na základě historických dat
/// </summary>
public class ActiveHours
{
    /// <summary>
    /// Začátek aktivních hodin (hodina 0-23)
    /// </summary>
    public int StartHour { get; set; }

    /// <summary>
    /// Konec aktivních hodin (hodina 0-23)
    /// </summary>
    public int EndHour { get; set; }

    /// <summary>
    /// Aktivní dny v týdnu
    /// </summary>
    public HashSet<DayOfWeek> ActiveDays { get; set; } = new HashSet<DayOfWeek>();

    /// <summary>
    /// Spolehlivost analýzy (0-1, kde 1 = velmi spolehlivé)
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// Kontrola, zda je daný čas v aktivních hodinách
    /// </summary>
    public bool IsActiveTime(DateTime dateTime)
    {
        // Kontrola aktivního dne
        if (!ActiveDays.Contains(dateTime.DayOfWeek))
        {
            return false;
        }

        // Kontrola aktivních hodin
        var hour = dateTime.Hour;

        // Zpracování případu, kdy aktivní doba přechází přes půlnoc
        if (StartHour > EndHour)
        {
            return hour >= StartHour || hour <= EndHour;
        }
        else
        {
            return hour >= StartHour && hour <= EndHour;
        }
    }
}
