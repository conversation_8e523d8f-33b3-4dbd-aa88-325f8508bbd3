import { Component, OnInit, AfterViewInit, OnD<PERSON>roy } from '@angular/core';
import { CommunicationPatternService } from '../../services/communication-pattern.service';
import { CommunicationPatternInfo, CommunicationOutageConfig, CommunicationOutage } from '../../models/communication-pattern.model';
import { TitleService } from '../../services/title.service';

declare var bootstrap: any;

@Component({
  selector: 'app-communication-patterns',
  templateUrl: './communication-patterns.component.html',
  styleUrls: ['./communication-patterns.component.css']
})
export class CommunicationPatternsComponent implements OnInit, AfterViewInit, OnDestroy {
  patterns: CommunicationPatternInfo[] = [];
  config: CommunicationOutageConfig | null = null;
  testResults: CommunicationOutage[] = [];
  loading = false;
  refreshing = false;
  testing = false;
  error: string | null = null;
  showTestResults = false;

  constructor(
    private communicationPatternService: CommunicationPatternService,
    private titleService: TitleService
  ) { }

  ngOnInit(): void {
    this.titleService.setTitle('Komunikační vzorce');
    this.loadData();
  }

  ngAfterViewInit(): void {
    // Inicializace popoverů s zpožděním, aby se zajistilo, že DOM je plně načten
    setTimeout(() => {
      this.initPopovers();
    }, 500);
  }

  ngOnDestroy(): void {
    // Zničení všech popoverů
    this.destroyPopovers();
  }

  private loadData(): void {
    this.loading = true;
    this.error = null;

    // Načtení vzorců
    this.communicationPatternService.getAllPatterns().subscribe({
      next: (response) => {
        if (response.success) {
          this.patterns = response.data;
        } else {
          this.error = response.message || 'Chyba při načítání vzorců';
        }
      },
      error: (error) => {
        this.error = 'Chyba při načítání vzorců: ' + (error.message || error);
        console.error('Chyba při načítání vzorců:', error);
      }
    });

    // Načtení konfigurace
    this.communicationPatternService.getConfiguration().subscribe({
      next: (response) => {
        if (response.success) {
          this.config = response.data;
        }
        this.loading = false;

        // Reinicializace popoverů po načtení dat
        setTimeout(() => {
          this.initPopovers();
        }, 100);
      },
      error: (error) => {
        console.error('Chyba při načítání konfigurace:', error);
        this.loading = false;
      }
    });
  }

  refreshPatterns(): void {
    this.refreshing = true;
    this.error = null;

    this.communicationPatternService.refreshPatterns().subscribe({
      next: (response) => {
        if (response.success) {
          // Po úspěšné aktualizaci znovu načti data
          this.loadData();
        } else {
          this.error = response.message || 'Chyba při aktualizaci vzorců';
        }
        this.refreshing = false;
      },
      error: (error) => {
        this.error = 'Chyba při aktualizaci vzorců: ' + (error.message || error);
        this.refreshing = false;
        console.error('Chyba při aktualizaci vzorců:', error);
      }
    });
  }

  testDetection(): void {
    this.testing = true;
    this.error = null;
    this.testResults = [];

    this.communicationPatternService.testDetection().subscribe({
      next: (response) => {
        if (response.success) {
          this.testResults = response.data;
          this.showTestResults = true;
        } else {
          this.error = response.message || 'Chyba při testování detekce';
        }
        this.testing = false;
      },
      error: (error) => {
        this.error = 'Chyba při testování detekce: ' + (error.message || error);
        this.testing = false;
        console.error('Chyba při testování detekce:', error);
      }
    });
  }



  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
        return 'badge bg-success';
      case 'inactive':
        return 'badge bg-secondary';
      case 'blocked':
        return 'badge bg-danger';
      default:
        return 'badge bg-secondary';
    }
  }

  getStatusText(status: string): string {
    switch (status.toLowerCase()) {
      case 'active':
        return 'Aktivní';
      case 'inactive':
        return 'Neaktivní';
      case 'blocked':
        return 'Blokovaná';
      default:
        return status;
    }
  }

  formatTimeSpan(timeSpan: string): string {
    // Převod TimeSpan stringu na čitelný formát
    const parts = timeSpan.split(':');
    if (parts.length >= 2) {
      const hours = parseInt(parts[0]);
      const minutes = parseInt(parts[1]);
      
      if (hours > 0) {
        return `${hours}h ${minutes}m`;
      } else {
        return `${minutes}m`;
      }
    }
    return timeSpan;
  }

  formatDate(date: Date | string | undefined): string {
    if (!date) return 'Nikdy';
    
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleString('cs-CZ');
  }

  formatNumber(num: number): string {
    return num.toFixed(1);
  }

  formatActiveHours(pattern: any): string {
    if (!pattern.activeHours) {
      return 'Neanalyzováno';
    }

    const ah = pattern.activeHours;
    const confidence = (ah.confidence * 100).toFixed(0);
    const days = ah.activeDays?.join(', ') || 'Žádné';

    return `${ah.startHour}:00-${ah.endHour}:00 (${confidence}% spolehlivost)`;
  }

  getActiveHoursTooltip(pattern: any): string {
    if (!pattern.activeHours) {
      return 'Aktivní hodiny nebyly analyzovány - nedostatek historických dat';
    }

    const ah = pattern.activeHours;
    const days = ah.activeDays?.join(', ') || 'Žádné dny';
    return `Aktivní dny: ${days}\nSpolehlivost: ${(ah.confidence * 100).toFixed(1)}%`;
  }

  getDetectionLogicTooltip(pattern: any): string {
    return pattern.detectionLogicDescription || 'Informace o detekční logice nejsou k dispozici';
  }

  getDetectionLogicShortText(detectionType: string): string {
    switch (detectionType) {
      case 'Nastavené pracovní hodiny':
        return 'Nastavené';
      case 'Historická analýza':
        return 'Historická';
      case 'Nepřetržitý monitoring':
        return 'Nepřetržitý';
      default:
        return 'Neznámý';
    }
  }

  getDetectionLogicBadgeClass(detectionType: string): string {
    switch (detectionType) {
      case 'Nastavené pracovní hodiny':
        return 'bg-success';
      case 'Historická analýza':
        return 'bg-info';
      case 'Nepřetržitý monitoring':
        return 'bg-warning';
      default:
        return 'bg-secondary';
    }
  }

  closeTestResults(): void {
    this.showTestResults = false;
    this.testResults = [];
  }



  /**
   * Inicializace popoverů pro nápovědu
   */
  private initPopovers(): void {
    // Definice obsahu nápověd
    const helpContent: Record<string, string> = {
      'analysis-window': 'Počet dní zpětně, ze kterých se analyzují komunikační vzorce instancí. ' +
        'Delší období poskytuje přesnější analýzu, ale vyžaduje více historických dat.',
      'tolerance': 'Tolerance v minutách pro detekci výpadků komunikace. ' +
        'Pokud instance nekomunikuje déle než očekávaný interval + tolerance, je detekován výpadek.',
      'min-frequency': 'Minimální frekvence komunikace za hodinu potřebná pro analýzu vzorce. ' +
        'Instance s nižší frekvencí nebudou analyzovány.',
      'exclude-weekends': 'Určuje, zda se mají víkendy vyloučit z analýzy komunikačních vzorců. ' +
        'Užitečné pro instance, které nekomunikují o víkendech.',
      'min-alert-interval': 'Minimální interval mezi alerty pro stejnou instanci v minutách. ' +
        'Zabraňuje spamování alertů při opakovaných výpadcích.',
      'pattern-update-interval': 'Interval v hodinách, jak často se aktualizují komunikační vzorce. ' +
        'Kratší interval znamená aktuálnější vzorce, ale vyšší zátěž systému.',
      'check-interval': 'Interval v minutách, jak často se kontrolují výpadky komunikace. ' +
        'Kratší interval znamená rychlejší detekci výpadků.',
      'min-calls-analysis': 'Minimální počet volání potřebný pro analýzu komunikačního vzorce instance. ' +
        'Instance s menším počtem volání nebudou analyzovány.'
    };

    // Nejprve zrušíme všechny existující popovery
    this.destroyPopovers();

    // Inicializace popoverů pomocí Bootstrap API
    document.querySelectorAll('[data-bs-toggle="popover"]').forEach(el => {
      const helpType = el.getAttribute('data-help-type');

      if (helpType && helpType in helpContent) {
        try {
          new bootstrap.Popover(el, {
            content: helpContent[helpType as keyof typeof helpContent],
            html: true,
            trigger: 'hover focus',
            placement: 'top',
            container: 'body',
            sanitize: false
          });
        } catch (error) {
          console.error('Chyba při inicializaci popoveru:', error);
        }
      } else if (helpType) {
        console.warn('Nápověda nenalezena pro typ:', helpType);
      }
    });
  }

  /**
   * Zničení všech popoverů
   */
  private destroyPopovers(): void {
    document.querySelectorAll('[data-bs-toggle="popover"]').forEach(el => {
      const popover = bootstrap.Popover.getInstance(el);
      if (popover) {
        popover.dispose();
      }
    });
  }
}
