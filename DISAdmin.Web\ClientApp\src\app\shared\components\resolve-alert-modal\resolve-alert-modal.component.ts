import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AlertResponse, SecurityEventResponse } from '../../../models/security.model';

// Interface pro AlertItem (z alerts.component.ts)
interface AlertItem {
  id: number;
  timestamp: Date;
  metricType?: string;
  alertRuleName?: string;
  message: string;
  description: string;
  alertType: string;
  severity: number;
  status: string;
  instanceId?: number;
  instanceName?: string;
  customerId?: number;
  customerName?: string;
  customerAbbreviation?: string;
  resolvedAt?: Date;
  resolvedBy?: string;
  isResolved: boolean;
  isIgnored?: boolean;
  alertRuleId?: number;
  condition?: string;
  threshold?: number;
  resolution?: string;
}

@Component({
  selector: 'app-resolve-alert-modal',
  templateUrl: './resolve-alert-modal.component.html',
  styleUrls: ['./resolve-alert-modal.component.css']
})
export class ResolveAlertModalComponent implements OnInit {
  @Input() alert: AlertItem | AlertResponse | null = null;
  @Input() securityEvent: SecurityEventResponse | null = null;
  @Input() modalId: string = 'resolveAlertModal';
  @Input() entityType: 'alert' | 'securityEvent' = 'alert';
  @Output() resolve = new EventEmitter<string>();
  @Output() ignore = new EventEmitter<void>();
  @Output() close = new EventEmitter<void>();

  resolveForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.resolveForm = this.fb.group({
      resolution: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
    // Inicializace formuláře
  }

  onResolve(): void {
    if (this.resolveForm.valid) {
      const resolution = this.resolveForm.get('resolution')?.value || '';
      this.resolve.emit(resolution);
      this.resetForm();
    }
  }

  onIgnore(): void {
    this.ignore.emit();
    this.resetForm();
  }

  onClose(): void {
    this.close.emit();
    this.resetForm();
  }

  private resetForm(): void {
    this.resolveForm.reset({
      resolution: ''
    });
  }

  /**
   * Získání aktuální entity (alert nebo security event)
   */
  getCurrentEntity(): AlertItem | AlertResponse | SecurityEventResponse | null {
    return this.entityType === 'alert' ? this.alert : this.securityEvent;
  }

  /**
   * Získání popisu aktuální entity
   */
  getCurrentDescription(): string {
    const entity = this.getCurrentEntity();
    if (!entity) return '';

    if (this.entityType === 'alert') {
      const alert = entity as AlertItem | AlertResponse;

      // Pokud má vlastnost message (AlertItem), použijeme ji jako fallback
      if ('message' in alert && alert.message) {
        return alert.description || alert.message;
      }

      // Jinak použijeme pouze description (AlertResponse)
      return alert.description || '';
    } else {
      return (entity as SecurityEventResponse).description || '';
    }
  }

  /**
   * Získání typu aktuální entity pro zobrazení
   */
  getCurrentType(): string {
    const entity = this.getCurrentEntity();
    if (!entity) return '';

    if (this.entityType === 'alert') {
      const alert = entity as AlertItem | AlertResponse;
      return alert.alertType || '';
    } else {
      return (entity as SecurityEventResponse).eventType || '';
    }
  }

  /**
   * Získání timestamp aktuální entity
   */
  getCurrentTimestamp(): Date {
    const entity = this.getCurrentEntity();
    if (!entity) return new Date();
    return entity.timestamp;
  }

  /**
   * Získání závažnosti aktuální entity
   */
  getCurrentSeverity(): number {
    const entity = this.getCurrentEntity();
    if (!entity) return 1;
    return entity.severity;
  }

  /**
   * Získání stavu aktuální entity
   */
  getCurrentStatus(): string {
    const entity = this.getCurrentEntity();
    if (!entity) return 'active';

    if (this.entityType === 'alert') {
      const alert = entity as AlertItem | AlertResponse;

      // Pokud má vlastnost status (AlertItem), použijeme ji
      if ('status' in alert && alert.status) {
        return alert.status;
      }

      // Jinak použijeme boolean vlastnosti (AlertResponse)
      return alert.isResolved ? 'resolved' : (alert.isIgnored ? 'ignored' : 'active');
    } else {
      const secEvent = entity as SecurityEventResponse;
      return secEvent.isResolved ? 'resolved' : (secEvent.isIgnored ? 'ignored' : 'active');
    }
  }

  /**
   * Získání názvu zákazníka
   */
  getCurrentCustomerName(): string {
    const entity = this.getCurrentEntity();
    if (!entity) return '';

    if (this.entityType === 'alert') {
      const alert = entity as AlertItem | AlertResponse;
      return alert.customerName || '';
    } else {
      // SecurityEvent nemá customerName
      return '';
    }
  }

  /**
   * Získání názvu instance
   */
  getCurrentInstanceName(): string {
    const entity = this.getCurrentEntity();
    if (!entity) return '';

    if (this.entityType === 'alert') {
      const alert = entity as AlertItem | AlertResponse;
      return alert.instanceName || '';
    } else {
      // SecurityEvent nemá instanceName
      return '';
    }
  }

  /**
   * Získání informací o pravidle (pouze pro alerty)
   */
  getCurrentAlertRule(): any {
    if (this.entityType !== 'alert') return null;
    const alert = this.alert as AlertItem | AlertResponse | any;
    if (!alert) return null;

    // Pokud má alert vazbu na pravidlo, vrátíme informace o pravidle
    if (alert.alertRuleId && alert.alertRuleName) {
      return {
        name: alert.alertRuleName,
        condition: this.getConditionText(alert.condition || 'gt'),
        threshold: this.formatThreshold(alert.metricType, alert.threshold)
      };
    }

    // Pro communicationOutage alerty bez explicitního pravidla nebo s pravidlem ale bez názvu
    if (alert.metricType === 'communicationOutage' || alert.alertType === 'communicationOutage') {
      return {
        name: alert.alertRuleName || 'Výpadek komunikace DIS instancí',
        condition: this.getConditionText(alert.condition || 'gt'),
        threshold: alert.threshold ? this.formatThreshold(alert.metricType, alert.threshold) : this.getDefaultThreshold('communicationOutage')
      };
    }

    // Pro ostatní alerty s pravidlem
    if (alert.alertRuleId || alert.metricType) {
      return {
        name: alert.alertRuleName || this.getMetricTypeText(alert.metricType) || 'Neznámé pravidlo',
        condition: this.getConditionText(alert.condition || 'gt'),
        threshold: alert.threshold ? this.formatThreshold(alert.metricType, alert.threshold) : this.getDefaultThreshold(alert.metricType)
      };
    }

    return null;
  }

  /**
   * Formátování threshold hodnoty podle typu metriky
   */
  private formatThreshold(metricType: string, threshold: number): string {
    if (threshold === null || threshold === undefined) {
      return this.getDefaultThreshold(metricType);
    }

    switch (metricType) {
      case 'communicationOutage':
        return `${threshold} minut`;
      case 'apiResponseTime':
        return `${threshold} ms`;
      case 'errorRate':
        return `${threshold}%`;
      case 'apiCallsCount':
        return `${threshold} volání`;
      case 'certificateExpiration':
        return `${threshold} dní`;
      default:
        return threshold.toString();
    }
  }

  /**
   * Získání výchozího threshold pro typ metriky
   */
  private getDefaultThreshold(metricType: string): string {
    switch (metricType) {
      case 'communicationOutage':
        return '30 minut';
      case 'apiResponseTime':
        return '1000 ms';
      case 'errorRate':
        return '5%';
      default:
        return 'N/A';
    }
  }

  /**
   * Získání informací o řešení
   */
  getCurrentResolvedBy(): string {
    const entity = this.getCurrentEntity();
    if (!entity) return '';

    if (this.entityType === 'alert') {
      const alert = entity as AlertItem | AlertResponse;
      return alert.resolvedBy || '';
    } else {
      return (entity as SecurityEventResponse).resolvedBy || '';
    }
  }

  getCurrentResolvedAt(): Date | null {
    const entity = this.getCurrentEntity();
    if (!entity) return null;

    if (this.entityType === 'alert') {
      const alert = entity as AlertItem | AlertResponse;
      return alert.resolvedAt || null;
    } else {
      return (entity as SecurityEventResponse).resolvedAt || null;
    }
  }

  getCurrentResolution(): string {
    const entity = this.getCurrentEntity();
    if (!entity) return '';

    if (this.entityType === 'alert') {
      const alert = entity as AlertItem | AlertResponse;
      return alert.resolution || '';
    } else {
      return (entity as SecurityEventResponse).resolution || '';
    }
  }

  /**
   * Získání zobrazovaného typu (metricType pro alerty z pravidel, jinak přeložený alertType)
   */
  getDisplayType(): string {
    if (this.entityType === 'alert') {
      const alert = this.alert as AlertItem | AlertResponse | any;
      if (alert?.metricType) {
        return this.getMetricTypeText(alert.metricType);
      }
    }
    return this.getAlertTypeText(this.getCurrentType());
  }

  getSeverityClass(severity: number): string {
    switch (severity) {
      case 5:
        return 'text-danger fw-bold';
      case 4:
        return 'text-danger';
      case 3:
        return 'text-warning';
      case 2:
        return 'text-info';
      case 1:
        return 'text-success';
      default:
        return 'text-muted';
    }
  }

  getAlertTypeClass(alertType: string): string {
    switch (alertType) {
      case 'CertificateExpiring':
        return 'alert-warning';
      case 'FailedConnectionAttempts':
        return 'alert-warning';
      case 'SuspiciousActivity':
        return 'alert-danger';
      case 'ApiKeyMisuse':
        return 'alert-danger';
      case 'SystemError':
        return 'alert-danger';
      case 'Error':
        return 'alert-danger';
      case 'Warning':
        return 'alert-warning';
      case 'Information':
        return 'alert-info';
      default:
        return 'alert-info';
    }
  }

  getAlertTypeIcon(alertType: string): string {
    switch (alertType) {
      case 'CertificateExpiring':
        return 'bi-shield-exclamation';
      case 'FailedConnectionAttempts':
        return 'bi-x-circle-fill';
      case 'SuspiciousActivity':
        return 'bi-exclamation-triangle-fill';
      case 'ApiKeyMisuse':
        return 'bi-key-fill';
      case 'SystemError':
        return 'bi-exclamation-circle-fill';
      case 'communicationOutage':
        return 'bi-wifi-off';
      case 'Error':
        return 'bi-exclamation-circle-fill';
      case 'Warning':
        return 'bi-exclamation-triangle-fill';
      case 'Information':
        return 'bi-info-circle-fill';
      default:
        return 'bi-info-circle-fill';
    }
  }

  getAlertTypeText(alertType: string): string {
    // Alert typy
    switch (alertType) {
      case 'CertificateExpiring':
        return 'Expirující certifikát';
      case 'FailedConnectionAttempts':
        return 'Neúspěšné připojení';
      case 'SuspiciousActivity':
        return 'Podezřelá aktivita';
      case 'ApiKeyMisuse':
        return 'Chyba použití API klíče';
      case 'SystemError':
        return 'Systémová chyba';
      case 'Other':
        return 'Ostatní';
      case 'Information':
        return 'Informace';
      case 'Warning':
        return 'Varování';
      case 'Error':
        return 'Chyba';
      // SecurityEvent typy
      case 'FailedAccessAttempt':
        return 'Neúspěšný pokus o přístup';
      case 'IpBlocked':
        return 'Blokovaná IP adresa';
      case 'CertificateValidationFailure':
        return 'Selhání validace certifikátu';
      case 'UnauthorizedAccess':
        return 'Neoprávněný přístup';
      case 'UnauthorizedIpAccess':
        return 'Přístup z nepovolené IP adresy';
      default:
        return alertType;
    }
  }

  /**
   * Získání textu pro typ metriky
   */
  getMetricTypeText(metricType: string | null | undefined): string {
    if (!metricType) {
      return 'Systémový alert';
    }

    switch (metricType) {
      case 'certificateExpiration':
        return 'Expirace certifikátu';
      case 'apiResponseTime':
        return 'Doba odezvy API';
      case 'apiCallCount':
        return 'Počet API volání';
      case 'errorRate':
        return 'Míra chyb';
      case 'diskSpace':
        return 'Místo na disku';
      case 'memoryUsage':
        return 'Využití paměti';
      case 'cpuUsage':
        return 'Využití CPU';
      case 'connectionCount':
        return 'Počet připojení';
      case 'failedLogins':
        return 'Neúspěšná přihlášení';
      case 'suspiciousActivity':
        return 'Podezřelá aktivita';
      case 'unauthorizedAccess':
        return 'Neoprávněný přístup';
      case 'dataIntegrity':
        return 'Integrita dat';
      case 'backupStatus':
        return 'Stav zálohování';
      case 'serviceAvailability':
        return 'Dostupnost služby';
      case 'networkLatency':
        return 'Latence sítě';
      case 'databasePerformance':
        return 'Výkon databáze';
      case 'logErrors':
        return 'Chyby v logu';
      case 'securityEvents':
        return 'Bezpečnostní události';
      case 'systemLoad':
        return 'Zatížení systému';
      case 'communicationOutage':
        return 'Výpadek komunikace';
      case 'custom':
        return 'Vlastní metrika';
      default:
        return metricType;
    }
  }

  /**
   * Získání textu pro závažnost
   */
  getSeverityText(severity: number): string {
    switch (severity) {
      case 3:
        return 'Kritický';
      case 2:
        return 'Varování';
      case 1:
        return 'Informace';
      default:
        return 'Neznámá';
    }
  }

  /**
   * Získání textu pro stav
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'active':
        return 'Aktivní';
      case 'resolved':
        return 'Vyřešeno';
      case 'ignored':
        return 'Ignorováno';
      default:
        return status;
    }
  }

  /**
   * Získání CSS třídy pro stav
   */
  getStatusClass(status: string): string {
    switch (status) {
      case 'active':
        return 'bg-danger text-white';
      case 'resolved':
        return 'bg-success text-white';
      case 'ignored':
        return 'bg-warning text-dark';
      default:
        return 'bg-secondary text-white';
    }
  }

  /**
   * Získání textu pro podmínku
   */
  getConditionText(condition: string): string {
    switch (condition) {
      case 'gt':
        return 'Větší než';
      case 'lt':
        return 'Menší než';
      case 'eq':
        return 'Rovno';
      case 'gte':
        return 'Větší nebo rovno';
      case 'lte':
        return 'Menší nebo rovno';
      default:
        return condition;
    }
  }
}
