<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="mb-4">
        <h2>
          <i class="bi bi-diagram-3-fill me-2"></i>
          Komunikační vzorce instancí
        </h2>
        <p class="text-muted mb-0">
          Analýza historického chování komunikace instancí pro inteligentní detekci výpadků.
          Instance bez nastavených pracovních hodin používají automaticky analyzované aktivní hodiny.
        </p>
      </div>

      <!-- Error <PERSON> -->
      <div *ngIf="error" class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        {{ error }}
        <button type="button" class="btn-close" (click)="error = null"></button>
      </div>

      <!-- Loading -->
      <div *ngIf="loading" class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Načítám...</span>
        </div>
        <p class="mt-2">Načítám komunikační vzorce...</p>
      </div>

      <!-- Configuration Card -->
      <div *ngIf="config && !loading" class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">
            <i class="bi bi-gear-fill me-2"></i>
            Konfigurace detekce výpadků
          </h5>
          <div>
            <button class="btn btn-outline-primary btn-sm me-2"
                    (click)="testDetection()"
                    [disabled]="testing">
              <i class="bi bi-play-circle" [class.spinner-border]="testing" [class.spinner-border-sm]="testing"></i>
              <span *ngIf="!testing"> Testovat detekci</span>
              <span *ngIf="testing"> Testování...</span>
            </button>
            <button class="btn btn-primary btn-sm"
                    (click)="refreshPatterns()"
                    [disabled]="refreshing">
              <i class="bi bi-arrow-clockwise" [class.spinner-border]="refreshing" [class.spinner-border-sm]="refreshing"></i>
              <span *ngIf="!refreshing"> Aktualizovat vzorce</span>
              <span *ngIf="refreshing"> Aktualizuji...</span>
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <strong data-bs-toggle="popover" data-help-type="analysis-window" style="cursor: help;">Okno analýzy:</strong> {{ config.analysisWindowDays }} dní
            </div>
            <div class="col-md-3">
              <strong data-bs-toggle="popover" data-help-type="tolerance" style="cursor: help;">Tolerance:</strong> {{ config.toleranceMinutes }} minut
            </div>
            <div class="col-md-3">
              <strong data-bs-toggle="popover" data-help-type="min-frequency" style="cursor: help;">Min. frekvence:</strong> {{ config.minimumFrequencyPerHour }}/hod
            </div>
            <div class="col-md-3">
              <strong data-bs-toggle="popover" data-help-type="exclude-weekends" style="cursor: help;">Vyloučit víkendy: </strong>
              <span class="badge" [class]="config.excludeWeekends ? 'bg-success' : 'bg-secondary'">
                {{ config.excludeWeekends ? 'Ano' : 'Ne' }}
              </span>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-3">
              <strong data-bs-toggle="popover" data-help-type="min-alert-interval" style="cursor: help;">Min. interval alertů:</strong> {{ config.minimumAlertInterval }} min
            </div>
            <div class="col-md-3">
              <strong data-bs-toggle="popover" data-help-type="pattern-update-interval" style="cursor: help;">Aktualizace vzorců:</strong> {{ config.patternUpdateIntervalHours }} hod
            </div>
            <div class="col-md-3">
              <strong data-bs-toggle="popover" data-help-type="check-interval" style="cursor: help;">Interval kontrol:</strong> {{ config.checkIntervalMinutes }} min
            </div>
            <div class="col-md-3">
              <strong data-bs-toggle="popover" data-help-type="min-calls-analysis" style="cursor: help;">Min. volání pro analýzu:</strong> {{ config.minimumCallsForAnalysis }}
            </div>
          </div>
        </div>
      </div>

      <!-- Patterns Table -->
      <app-collapsible-block *ngIf="!loading"
                             title="Komunikační vzorce"
                             icon="bi-table"
                             [showRecordCount]="true"
                             [recordCount]="patterns.length"
                             storageKey="collapsible_block_communication_patterns"
                             [defaultExpanded]="true"
                             marginTop=""
                             marginBottom="mb-4">
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th class="text-nowrap">Zákazník</th>
                <th class="text-nowrap">Instance</th>
                <th class="text-nowrap">Stav</th>
                <th class="text-nowrap">Typ detekce</th>
                <th class="text-nowrap">Aktivní hodiny</th>
                <th class="text-nowrap" style="width: 120px;">Volání za 30 dní</th>
                <th class="text-nowrap">Průměr (min)</th>
                <th class="text-nowrap">Std. odchylka</th>
                <th class="text-nowrap">Min/max (min)</th>
                <th class="text-nowrap" style="width: 140px;">Posl. kontakt</th>
                <th class="text-nowrap">Dostatek dat</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of patterns">
                <td>
                  <strong>{{ item.customerAbbreviation }}</strong>
                </td>
                <td class="text-nowrap">
                  <strong>{{ item.instanceName }}</strong>
                </td>
                <td>
                  <span [class]="getStatusBadgeClass(item.status)">
                    {{ getStatusText(item.status) }}
                  </span>
                </td>
                <td [title]="getDetectionLogicTooltip(item.pattern)">
                  <span class="badge" [class]="getDetectionLogicBadgeClass(item.pattern.detectionLogicType)">
                    {{ getDetectionLogicShortText(item.pattern.detectionLogicType) }}
                  </span>
                </td>
                <td [title]="getActiveHoursTooltip(item.pattern)">
                  <small>{{ formatActiveHours(item.pattern) }}</small>
                </td>
                <td class="text-center text-nowrap">
                  <strong>{{ item.pattern.totalCallsAnalyzed }}</strong>
                </td>
                <td class="text-end">
                  {{ formatNumber(item.pattern.averageIntervalMinutes) }}
                </td>
                <td class="text-end">
                  {{ formatNumber(item.pattern.standardDeviation) }}
                </td>
                <td class="text-center">
                  {{ formatNumber(item.pattern.minIntervalMinutes) }} /
                  {{ formatNumber(item.pattern.maxIntervalMinutes) }}
                </td>
                <td class="text-nowrap">
                  {{ formatDate(item.lastConnectionDate) }}
                </td>
                <td class="text-center">
                  <span class="badge" [class]="item.pattern.hasSufficientData ? 'bg-success' : 'bg-warning'">
                    {{ item.pattern.hasSufficientData ? 'Ano' : 'Ne' }}
                  </span>
                </td>
              </tr>
              <tr *ngIf="patterns.length === 0">
                <td colspan="11" class="text-center py-4 text-muted">
                  <i class="bi bi-inbox fs-1"></i>
                  <p class="mt-2">Žádné komunikační vzorce nebyly nalezeny</p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </app-collapsible-block>
    </div>
  </div>
</div>

<!-- Test Results Modal -->
<div class="modal fade" [class.show]="showTestResults" [style.display]="showTestResults ? 'block' : 'none'" 
     tabindex="-1" role="dialog" *ngIf="showTestResults">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="bi bi-bug-fill me-2"></i>
          Výsledky testování detekce výpadků
        </h5>
        <button type="button" class="btn-close" (click)="closeTestResults()"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="testResults.length === 0" class="text-center py-4">
          <i class="bi bi-check-circle-fill text-success fs-1"></i>
          <p class="mt-2">Žádné výpadky komunikace nebyly detekovány</p>
        </div>
        <div *ngIf="testResults.length > 0">
          <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            Detekováno {{ testResults.length }} výpadků komunikace
          </div>
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Instance</th>
                  <th>Zákazník</th>
                  <th>Čas od kontaktu</th>
                  <th>Očekávaný max</th>
                  <th>Poslední kontakt</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let outage of testResults">
                  <td>{{ outage.instanceName }}</td>
                  <td>{{ outage.customerAbbreviation }}</td>
                  <td>{{ formatTimeSpan(outage.timeSinceLastContact) }}</td>
                  <td>{{ formatTimeSpan(outage.expectedMaxInterval) }}</td>
                  <td>{{ formatDate(outage.lastContactTime) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeTestResults()">Zavřít</button>
      </div>
    </div>
  </div>
</div>
<div class="modal-backdrop fade" [class.show]="showTestResults" *ngIf="showTestResults"></div>