import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AlertService } from '../services/alert.service';
import { AuthService } from '../services/auth.service';
import { SignalRService } from '../services/signalr.service';
import { Subscription, interval } from 'rxjs';
import { FilterField } from '../shared/advanced-filter/advanced-filter.component';
import { CustomerService } from '../services/customer.service';
import { InstanceService } from '../services/instance.service';
import { ToastrService } from 'ngx-toastr';
import { ModalService } from '../services/modal.service';

interface AlertItem {
  id: number;
  timestamp: Date;
  metricType?: string; // Typ metriky z pravidla
  alertRuleName?: string; // Název pravidla
  message: string;
  description: string;
  alertType: string; // Pro kompatibilitu s ResolveAlertModalComponent
  severity: number;
  status: string;
  instanceId?: number;
  instanceName?: string;
  customerId?: number;
  customerName?: string;
  customerAbbreviation?: string;
  resolvedAt?: Date;
  resolvedBy?: string;
  isResolved: boolean;
  isIgnored?: boolean;
  alertRuleId?: number; // ID pravidla
  condition?: string; // Podmínka z pravidla
  threshold?: number; // Threshold z pravidla
}

@Component({
  selector: 'app-alerts',
  templateUrl: './alerts.component.html',
  styleUrls: ['./alerts.component.css']
})
export class AlertsComponent implements OnInit, OnDestroy {
  alerts: AlertItem[] = [];
  alertRules: any[] = [];
  loading = true;
  error: string | null = null;
  currentUser: any;
  isAdmin = false;

  // Formuláře
  alertRuleForm: FormGroup;

  // Vybraný záznam
  selectedRule: any = null;

  // Filtry
  filterForm: FormGroup;
  filterFields: FilterField[] = [];

  // Aktualizace dat
  private signalRSubscriptions: Subscription[] = [];
  private updateSubscription: Subscription | null = null;

  // Data pro filtry
  customers: any[] = [];
  instances: any[] = [];

  // Vybraný alert pro řešení
  selectedAlert: AlertItem | null = null;

  // Indikátor, zda byly filtry inicializovány
  private filtersInitialized = false;

  constructor(
    private alertService: AlertService,
    private authService: AuthService,
    private signalRService: SignalRService,
    private fb: FormBuilder,
    private customerService: CustomerService,
    private instanceService: InstanceService,
    private router: Router,
    private toastr: ToastrService,
    private modalService: ModalService
  ) {
    this.authService.currentUser.subscribe(user => {
      this.currentUser = user;
      this.isAdmin = user?.isAdmin || false;
    });

    this.alertRuleForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(200)]],
      description: ['', Validators.maxLength(500)],
      metricType: ['', Validators.required],
      condition: ['', [Validators.required, Validators.maxLength(50)]],
      threshold: ['', [Validators.required, Validators.min(0)]],
      severity: ['warning', [Validators.required, Validators.maxLength(20)]],
      enabled: [true],
      notifyByEmail: [false],
      emailRecipients: ['', Validators.maxLength(500)],
      customerId: [''],
      instanceId: ['']
    });

    // Výchozí hodnoty filtru podle požadavků - odpovídají obrázku
    this.filterForm = this.fb.group({
      severity: ['all'],
      status: ['all'],
      dateRange: ['7'],
      customerId: [''],
      instanceId: ['']
    });
  }

  ngOnInit(): void {
    // Inicializace polí pro advancedFilter
    this.initFilterFields();

    // Načtení zákazníků pro filtry
    this.loadCustomers();

    // Načtení pravidel (nezávisí na filtrech)
    this.loadAlertRules();

    // Inicializace SignalR připojení
    this.initSignalR();

    // loadAlerts() se zavolá až po inicializaci filtrů v onFilterChange()

    // Validace emailů při změně notifyByEmail
    this.alertRuleForm.get('notifyByEmail')?.valueChanges.subscribe(value => {
      const emailRecipientsControl = this.alertRuleForm.get('emailRecipients');
      if (value) {
        emailRecipientsControl?.setValidators([
          Validators.required,
          Validators.pattern(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(,\s*[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$/),
          Validators.maxLength(500)
        ]);
      } else {
        emailRecipientsControl?.setValidators([Validators.maxLength(500)]);
      }
      emailRecipientsControl?.updateValueAndValidity();
    });

    // Sledování změny zákazníka pro načtení instancí
    this.filterForm.get('customerId')?.valueChanges.subscribe(customerId => {
      console.log('Změna zákazníka v hlavním formuláři:', customerId);
      if (customerId) {
        // Převod na číslo, protože hodnota z formuláře může být řetězec
        this.loadInstances(Number(customerId));
      } else {
        this.instances = [];
        this.updateInstanceOptions();
      }
    });

    // Sledování změny zákazníka ve formuláři pro pravidla
    this.alertRuleForm.get('customerId')?.valueChanges.subscribe(customerId => {
      if (customerId) {
        this.instanceService.getInstancesByCustomerId(Number(customerId)).subscribe({
          next: (instances) => {
            // Aktualizace seznamu instancí
            this.instances = instances;

            // Reset výběru instance
            const instanceControl = this.alertRuleForm.get('instanceId');
            instanceControl?.setValue('');
          },
          error: (err) => {
            console.error('Chyba při načítání instancí', err);
          }
        });
      } else {
        this.instances = [];
      }
    });
  }

  /**
   * Načtení seznamu zákazníků
   */
  loadCustomers(): void {
    this.customerService.getCustomers().subscribe({
      next: (data) => {
        console.log('Načtení zákazníků:', data);
        this.customers = data;
        this.updateCustomerOptions();

        // Pokud máme uložené ID zákazníka, načteme jeho instance
        const customerId = this.filterForm.get('customerId')?.value;
        if (customerId) {
          console.log('Načítání instancí pro uložené ID zákazníka:', customerId);
          this.loadInstances(Number(customerId));
        }
      },
      error: (err) => {
        console.error('Chyba při načítání zákazníků', err);
      }
    });
  }

  /**
   * Načtení seznamu instancí pro vybraného zákazníka
   */
  loadInstances(customerId: number): void {
    console.log('Načítání instancí pro zákazníka ID:', customerId);
    this.instanceService.getInstancesByCustomerId(customerId).subscribe({
      next: (data) => {
        console.log('Načtené instance:', data);
        this.instances = data;
        this.updateInstanceOptions();
      },
      error: (err) => {
        console.error('Chyba při načítání instancí', err);
      }
    });
  }

  /**
   * Aktualizace možností pro filtr zákazníků
   */
  updateCustomerOptions(): void {
    // Najdeme pole pro zákazníka
    const customerFieldIndex = this.filterFields.findIndex(f => f.name === 'customerId');
    if (customerFieldIndex !== -1) {
      // Vytvoříme nové pole options
      const options = [
        { value: '', label: 'Všichni zákazníci' }
      ];

      // Přidáme zákazníky
      this.customers.forEach(customer => {
        options.push({ value: customer.id, label: customer.name });
      });

      // Aktualizujeme pole
      this.filterFields[customerFieldIndex].options = options;

      // Aktualizace seznamu zákazníků v modálním okně pro pravidla
      const modalCustomerSelect = document.getElementById('customerId') as HTMLSelectElement;
      if (modalCustomerSelect) {
        // Vyčištění stávajících možností kromě první (Všichni zákazníci)
        while (modalCustomerSelect.options.length > 1) {
          modalCustomerSelect.remove(1);
        }

        // Přidání nových možností
        this.customers.forEach(customer => {
          const option = document.createElement('option');
          option.value = customer.id.toString();
          option.text = customer.name;
          modalCustomerSelect.add(option);
        });
      }
    }
  }

  /**
   * Aktualizace možností pro filtr instancí
   */
  updateInstanceOptions(): void {
    // Najdeme pole pro instance
    const instanceFieldIndex = this.filterFields.findIndex(f => f.name === 'instanceId');
    if (instanceFieldIndex !== -1) {
      // Vytvoříme nové pole options
      const options = [
        { value: '', label: 'Všechny instance' }
      ];

      // Přidáme instance
      this.instances
        .sort((a, b) => {
          // Nejprve seřadíme podle zkratky zákazníka
          const abbrevCompare = a.customerAbbreviation.localeCompare(b.customerAbbreviation);
          // Pokud jsou zkratky stejné, seřadíme podle názvu instance
          return abbrevCompare !== 0 ? abbrevCompare : a.name.localeCompare(b.name);
        })
        .forEach(instance => {
          options.push({ value: instance.id, label: `${instance.customerAbbreviation} - ${instance.name}` });
        });

      // Aktualizujeme pole
      this.filterFields[instanceFieldIndex].options = options;
    }
  }



  /**
   * Inicializace polí pro advancedFilter
   */
  initFilterFields(): void {
    this.filterFields = [
      {
        name: 'severity',
        label: 'Závažnost',
        type: 'select',
        options: [
          { value: 'all', label: 'Všechny' },
          { value: 'critical', label: 'Kritické' },
          { value: 'warning', label: 'Varování' },
          { value: 'info', label: 'Informace' }
        ]
      },
      {
        name: 'status',
        label: 'Stav',
        type: 'select',
        options: [
          { value: 'all', label: 'Všechny' },
          { value: 'active', label: 'Aktivní' },
          { value: 'resolved', label: 'Vyřešené' },
          { value: 'ignored', label: 'Ignorované' }
        ]
      },
      {
        name: 'dateRange',
        label: 'Časové období',
        type: 'select',
        options: [
          { value: '1', label: 'Posledních 1 den' },
          { value: '7', label: 'Posledních 7 dní' },
          { value: '30', label: 'Posledních 30 dní' },
          { value: '90', label: 'Posledních 90 dní' }
        ]
      },
      {
        name: 'customerId',
        label: 'Zákazník',
        type: 'select',
        options: [
          { value: '', label: 'Všichni zákazníci' }
          // Zde budou dynamicky načteni zákazníci
        ]
      },
      {
        name: 'instanceId',
        label: 'Instance',
        type: 'select',
        options: [
          { value: '', label: 'Všechny instance' }
          // Zde budou dynamicky načteny instance
        ]
      }
    ];
  }

  ngOnDestroy(): void {
    // Zrušení SignalR subscription
    this.signalRSubscriptions.forEach(sub => sub.unsubscribe());
    this.signalRService.stopConnection();

    // Zrušení subscription pro aktualizaci dat
    if (this.updateSubscription) {
      this.updateSubscription.unsubscribe();
    }
  }

  /**
   * Načtení alertů
   */
  loadAlerts(): void {
    this.loading = true;
    const filters = this.getFilters();

    this.alertService.getAlerts(
      filters.severity,
      filters.status,
      parseInt(filters.dateRange),
      filters.customerId,
      filters.instanceId
    ).subscribe({
      next: (data) => {
        this.alerts = data;
        this.loading = false;
      },
      error: (err) => {
        console.error('Chyba při načítání alertů', err);
        this.error = 'Nepodařilo se načíst alerty';
        this.loading = false;
      }
    });
  }

  /**
   * Načtení pravidel pro alerty
   */
  loadAlertRules(): void {
    this.alertService.getAlertRules().subscribe({
      next: (data) => {
        this.alertRules = data;
      },
      error: (err) => {
        console.error('Chyba při načítání pravidel pro alerty', err);
        this.error = 'Nepodařilo se načíst pravidla pro alerty';
      }
    });
  }

  /**
   * Získání aktuálních filtrů
   */
  getFilters(): any {
    const formValues = this.filterForm.value;
    return {
      severity: formValues.severity,
      status: formValues.status,
      dateRange: formValues.dateRange,
      customerId: formValues.customerId || undefined,
      instanceId: formValues.instanceId || undefined
    };
  }

  /**
   * Zpracování změny filtru z komponenty advancedFilter
   */
  onFilterChange(filters: any): void {
    // Nastavení výchozích hodnot, pokud nejsou definovány - musí odpovídat výchozím hodnotám v initFilterFields
    const filterValues = {
      severity: filters.severity || 'all',
      status: filters.status || 'all',
      dateRange: filters.dateRange || '7',
      customerId: filters.customerId || '',
      instanceId: filters.instanceId || ''
    };

    console.log('Změna filtru:', filterValues);

    // Uložení aktuální hodnoty customerId
    const oldCustomerId = this.filterForm.get('customerId')?.value;

    // Aktualizace formuláře
    this.filterForm.patchValue(filterValues, { emitEvent: false });

    // Pokud se změnil zákazník, načteme instance
    if (oldCustomerId !== filterValues.customerId && filterValues.customerId) {
      console.log('Změna zákazníka v onFilterChange:', oldCustomerId, '->', filterValues.customerId);
      this.loadInstances(Number(filterValues.customerId));
    }

    // Označíme, že filtry byly inicializovány
    if (!this.filtersInitialized) {
      this.filtersInitialized = true;
      console.log('Filtry byly inicializovány pro alerts modul');
    }

    // Načtení dat s novými filtry
    this.loadAlerts();
  }

  /**
   * Přechod na stránku pro přidání nového pravidla
   */
  addRule(): void {
    this.router.navigate(['/alerts/rules/add']);
  }

  /**
   * Přechod na stránku pro úpravu pravidla
   */
  editRule(rule: any): void {
    this.router.navigate(['/alerts/rules', rule.id]);
  }



  /**
   * Smazání pravidla
   */
  deleteRule(ruleId: number): void {
    if (confirm('Opravdu chcete smazat toto pravidlo?')) {
      this.alertService.deleteAlertRule(ruleId).subscribe({
        next: () => {
          this.toastr.success('Pravidlo bylo úspěšně smazáno', 'Úspěch');
          this.loadAlertRules();
        },
        error: (err) => {
          console.error('Chyba při mazání pravidla', err);
          this.error = 'Nepodařilo se smazat pravidlo';
        }
      });
    }
  }

  /**
   * Změna stavu pravidla (povoleno/zakázáno)
   */
  toggleRuleStatus(rule: any): void {
    const updatedRule = { ...rule, enabled: !rule.enabled };
    this.alertService.updateAlertRule(updatedRule).subscribe({
      next: () => {
        this.toastr.success(`Pravidlo bylo ${updatedRule.enabled ? 'aktivováno' : 'deaktivováno'}`, 'Úspěch');
        this.loadAlertRules();
      },
      error: (err) => {
        console.error('Chyba při změně stavu pravidla', err);
        this.error = 'Nepodařilo se změnit stav pravidla';
      }
    });
  }

  /**
   * Otevření modalu pro řešení alertu
   */
  openResolveAlertModal(alert: AlertItem): void {
    this.selectedAlert = alert;
    this.modalService.open('resolveAlertModal');
  }

  /**
   * Vyřešení alertu s popisem řešení
   */
  onResolveAlert(resolution: string): void {
    if (!this.selectedAlert) {
      return;
    }

    this.alertService.resolveAlert(this.selectedAlert.id, resolution).subscribe({
      next: () => {
        this.toastr.success('Alert byl úspěšně vyřešen', 'Úspěch');
        this.loadAlerts();
      },
      error: (err) => {
        console.error('Chyba při řešení alertu', err);
        this.error = 'Nepodařilo se vyřešit alert';
        this.toastr.error('Chyba při řešení alertu', 'Chyba');
      }
    });
  }

  /**
   * Ignorování alertu
   */
  onIgnoreAlert(): void {
    if (!this.selectedAlert) {
      return;
    }

    this.alertService.ignoreAlert(this.selectedAlert.id).subscribe({
      next: () => {
        this.toastr.success('Alert byl ignorován', 'Úspěch');
        this.loadAlerts();
      },
      error: (err) => {
        console.error('Chyba při ignorování alertu', err);
        this.error = 'Nepodařilo se ignorovat alert';
        this.toastr.error('Chyba při ignorování alertu', 'Chyba');
      }
    });
  }

  /**
   * Inicializace SignalR připojení
   */
  private async initSignalR(): Promise<void> {
    try {
      console.log('Initializing SignalR connection...');
      await this.signalRService.startConnection();

      // Otestování připojení
      const isConnected = await this.signalRService.testConnection();
      if (!isConnected) {
        console.warn('SignalR connection test failed. Falling back to interval-based updates.');
        return;
      }

      console.log('SignalR connection test successful. Joining groups...');

      // Připojení do skupin pro odebírání alertů
      await this.signalRService.joinGroup('alerts');

      // Registrace subscription pro real-time aktualizace
      this.signalRSubscriptions.push(
        this.signalRService.alerts$.subscribe(data => {
          if (Object.keys(data).length > 0) {
            this.loadAlerts();
          }
        })
      );

      console.log('SignalR initialization completed successfully.');
    } catch (error) {
      console.error('Error during SignalR initialization:', error);
      console.warn('Falling back to interval-based updates.');
    }
  }

  /**
   * Získání CSS třídy pro závažnost alertu
   * @deprecated Použijte místo toho přímo třídy severity-high, severity-medium, severity-low
   */
  getSeverityClass(severity: string): string {
    switch (severity) {
      case 'critical':
        return 'severity-high';
      case 'warning':
        return 'severity-medium';
      case 'info':
        return 'severity-low';
      default:
        return 'bg-secondary';
    }
  }

  /**
   * Získání textu pro typ metriky
   */
  getMetricTypeText(metricType: string | null | undefined): string {
    if (!metricType) {
      return '-'; // Pro alerty bez vazby na pravidlo
    }

    switch (metricType) {
      // Existující metriky
      case 'apiResponseTime':
        return 'Doba odezvy API';
      case 'apiCallsCount':
        return 'Počet API volání';
      case 'errorRate':
        return 'Míra chyb';
      case 'certificateExpiration':
        return 'Expirace certifikátu';
      case 'communicationOutage':
        return 'Výpadek komunikace';
      case 'failedConnectionAttempts':
        return 'Neúspěšné pokusy o připojení';
      case 'suspiciousActivities':
        return 'Podezřelé aktivity';
      case 'apiAccessNonWorkHours':
        return 'Přístupy k API mimo pracovní dobu';
      case 'unauthorizedIpAccess':
        return 'Přístupy z nepovolených IP adres';
      case 'apiAccessHighCount':
        return 'Neobvykle vysoký počet přístupů k API';
      case 'apiAccessLowCount':
        return 'Neobvykle nízký počet přístupů k API';
      case 'failedLogins':
        return 'Neúspěšné pokusy o přihlášení';
      case 'securityEvents':
        return 'Bezpečnostní události';

      // Metriky pro výkonnostní anomálie - absolutní hodnoty
      case 'methodResponseTime95Percentile':
        return '95. percentil doby odezvy metody';
      case 'methodResponseTimeMax':
        return 'Maximální doba odezvy metody';
      case 'methodResponseTimeStdDev':
        return 'Variabilita doby odezvy metody';
      case 'methodCallCount':
        return 'Počet volání metody';

      // Metriky pro výkonnostní anomálie - relativní změny
      case 'methodResponseTimeChange':
        return 'Změna doby odezvy metody';
      case 'methodCallCountChange':
        return 'Změna počtu volání metody';

      // Metriky pro výkonnostní anomálie - trendy
      case 'methodResponseTimeTrend':
        return 'Trend doby odezvy metody';
      case 'methodCallCountTrend':
        return 'Trend počtu volání metody';

      // Metriky pro výkonnostní anomálie - korelace
      case 'methodResponseTimeCallCountCorrelation':
        return 'Korelace odezvy a počtu volání';
      case 'methodResponseTimeMedianRatio':
        return 'Poměr průměr/medián doby odezvy';

      // Ostatní metriky
      case 'diskSpace':
        return 'Místo na disku';
      case 'memoryUsage':
        return 'Využití paměti';
      case 'cpuUsage':
        return 'Využití CPU';
      case 'connectionCount':
        return 'Počet připojení';
      case 'suspiciousActivity':
        return 'Podezřelá aktivita';
      case 'unauthorizedAccess':
        return 'Neoprávněný přístup';
      case 'dataIntegrity':
        return 'Integrita dat';
      case 'backupStatus':
        return 'Stav zálohování';
      case 'serviceAvailability':
        return 'Dostupnost služby';
      case 'networkLatency':
        return 'Latence sítě';
      case 'databasePerformance':
        return 'Výkon databáze';
      case 'logErrors':
        return 'Chyby v logu';
      case 'systemLoad':
        return 'Zatížení systému';
      case 'custom':
        return 'Vlastní metrika';
      default:
        return metricType;
    }
  }

  /**
   * Zkrácení zprávy pro zobrazení v tabulce
   */
  getTruncatedMessage(message: string): string {
    const maxLength = 80; // Maximální délka zprávy
    if (message && message.length > maxLength) {
      return message.substring(0, maxLength) + '...';
    }
    return message || '';
  }

  /**
   * Získání textu pro závažnost alertu
   */
  getSeverityText(severity: number): string {
    switch (severity) {
      case 3:
        return 'Kritický';
      case 2:
        return 'Varování';
      case 1:
        return 'Informace';
      default:
        return 'Neznámá';
    }
  }

  /**
   * Získání CSS třídy pro stav alertu
   */
  getStatusClass(status: string): string {
    switch (status) {
      case 'active':
        return 'bg-danger';
      case 'resolved':
        return 'bg-success';
      case 'ignored':
        return 'bg-warning text-dark';
      case 'acknowledged':
        return 'bg-info text-dark';
      default:
        return 'bg-secondary';
    }
  }

  /**
   * Získání textu pro stav alertu
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'active':
        return 'Aktivní';
      case 'resolved':
        return 'Vyřešený';
      case 'ignored':
        return 'Ignorovaný';
      case 'acknowledged':
        return 'Potvrzený';
      default:
        return status;
    }
  }



  /**
   * Získání ikony pro typ metriky
   */
  getMetricTypeIcon(metricType: string): string {
    switch (metricType) {
      // Existující metriky
      case 'apiResponseTime':
        return 'bi-speedometer2';
      case 'apiCallsCount':
        return 'bi-graph-up';
      case 'errorRate':
        return 'bi-exclamation-triangle';
      case 'certificateExpiration':
        return 'bi-shield-lock';
      case 'communicationOutage':
        return 'bi-wifi-off';
      case 'failedConnectionAttempts':
        return 'bi-x-circle';
      case 'suspiciousActivities':
        return 'bi-eye';
      case 'apiAccessNonWorkHours':
        return 'bi-clock';
      case 'unauthorizedIpAccess':
        return 'bi-shield-x';
      case 'apiAccessHighCount':
        return 'bi-arrow-up-circle';
      case 'apiAccessLowCount':
        return 'bi-arrow-down-circle';
      case 'failedLogins':
        return 'bi-person-x';
      case 'securityEvents':
        return 'bi-shield-exclamation';

      // Metriky pro výkonnostní anomálie - absolutní hodnoty
      case 'methodResponseTime95Percentile':
        return 'bi-stopwatch';
      case 'methodResponseTimeMax':
        return 'bi-alarm';
      case 'methodResponseTimeStdDev':
        return 'bi-distribute-vertical';
      case 'methodCallCount':
        return 'bi-hash';

      // Metriky pro výkonnostní anomálie - relativní změny
      case 'methodResponseTimeChange':
        return 'bi-arrow-left-right';
      case 'methodCallCountChange':
        return 'bi-arrow-repeat';

      // Metriky pro výkonnostní anomálie - trendy
      case 'methodResponseTimeTrend':
        return 'bi-graph-up-arrow';
      case 'methodCallCountTrend':
        return 'bi-bar-chart-line';

      // Metriky pro výkonnostní anomálie - korelace
      case 'methodResponseTimeCallCountCorrelation':
        return 'bi-link';
      case 'methodResponseTimeMedianRatio':
        return 'bi-percent';

      default:
        return 'bi-question-circle';
    }
  }

  /**
   * Formátování hodnoty threshold podle typu metriky
   */
  formatThreshold(metricType: string, threshold: number): string {
    switch (metricType) {
      // Metriky s jednotkou ms
      case 'apiResponseTime':
      case 'methodResponseTime95Percentile':
      case 'methodResponseTimeMax':
      case 'methodResponseTimeStdDev':
        return `${threshold} ms`;

      // Metriky s jednotkou %
      case 'errorRate':
      case 'methodResponseTimeChange':
      case 'methodCallCountChange':
      case 'methodResponseTimeTrend':
      case 'methodCallCountTrend':
      case 'methodResponseTimeCallCountCorrelation':
      case 'apiAccessNonWorkHours':
        return `${threshold} %`;

      // Metriky s jednotkou dnů
      case 'certificateExpiration':
        return `${threshold} dnů`;

      // Metriky s jednotkou minut
      case 'communicationOutage':
        return `${threshold} minut`;

      // Metriky bez jednotky
      case 'apiCallsCount':
      case 'methodCallCount':
      case 'methodResponseTimeMedianRatio':
      case 'failedConnectionAttempts':
      case 'suspiciousActivities':
      case 'apiAccessHighCount':
      case 'apiAccessLowCount':
      case 'failedLogins':
      case 'unauthorizedIpAccess':
        return threshold.toString();

      default:
        return threshold.toString();
    }
  }

  /**
   * Získání CSS třídy pro hodnotu threshold podle typu metriky
   */
  getThresholdClass(metricType: string, threshold: number): string {
    let cssClass = 'threshold-value';

    switch (metricType) {
      case 'apiResponseTime':
        if (threshold > 1000) cssClass += ' threshold-critical';
        else if (threshold > 500) cssClass += ' threshold-warning';
        else if (threshold > 200) cssClass += ' threshold-info';
        else cssClass += ' threshold-normal';
        break;
      case 'errorRate':
        if (threshold > 10) cssClass += ' threshold-critical';
        else if (threshold > 5) cssClass += ' threshold-warning';
        else if (threshold > 1) cssClass += ' threshold-info';
        else cssClass += ' threshold-normal';
        break;
      case 'certificateExpiration':
        if (threshold < 7) cssClass += ' threshold-critical';
        else if (threshold < 14) cssClass += ' threshold-warning';
        else if (threshold < 30) cssClass += ' threshold-info';
        else cssClass += ' threshold-normal';
        break;
      case 'apiCallsCount':
        if (threshold > 10000) cssClass += ' threshold-critical';
        else if (threshold > 5000) cssClass += ' threshold-warning';
        else if (threshold > 1000) cssClass += ' threshold-info';
        else cssClass += ' threshold-normal';
        break;
      case 'communicationOutage':
        if (threshold > 240) cssClass += ' threshold-critical'; // 4+ hodin
        else if (threshold > 120) cssClass += ' threshold-warning'; // 2+ hodiny
        else if (threshold > 60) cssClass += ' threshold-info'; // 1+ hodina
        else cssClass += ' threshold-normal';
        break;
      default:
        cssClass += ' threshold-normal';
    }

    return cssClass;
  }

  /**
   * Získání textu pro podmínku
   */
  getConditionText(condition: string): string {
    switch (condition) {
      case 'greaterThan':
        return 'Větší než';
      case 'lessThan':
        return 'Menší než';
      case 'equals':
        return 'Rovno';
      case 'notEquals':
        return 'Nerovno';
      default:
        return condition;
    }
  }
}
