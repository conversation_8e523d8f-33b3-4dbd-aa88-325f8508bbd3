using DISAdmin.Api.Models;
using DISAdmin.Core.Data;
using DISAdmin.Core.Data.Entities;
using Microsoft.EntityFrameworkCore;

namespace DISAdmin.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Auth.Authorize(AdminOnly = true)]
public class AlertsController : ControllerBase
{
    private readonly DISAdminDbContext _context;
    private readonly ILogger<AlertsController> _logger;

    public AlertsController(DISAdminDbContext context, ILogger<AlertsController> logger)
    {
        _context = context;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> GetAlerts(
        [FromQuery] string? severity = null,
        [FromQuery] string? status = null,
        [FromQuery] int days = 7,
        [FromQuery] int? customerId = null,
        [FromQuery] int? instanceId = null)
    {
        try
        {
            var query = _context.Alerts.AsQueryable();

            // Filtrování podle zá<PERSON>nosti (nyní podle číselné hodnoty)
            if (!string.IsNullOrEmpty(severity) && severity != "all")
            {
                if (int.TryParse(severity, out int severityValue))
                {
                    query = query.Where(a => a.Severity == severityValue);
                }
            }

            // Filtrování podle stavu
            if (!string.IsNullOrEmpty(status) && status != "all")
            {
                if (status == "resolved")
                {
                    query = query.Where(a => a.IsResolved);
                }
                else if (status == "ignored")
                {
                    query = query.Where(a => a.IsIgnored);
                }
                else if (status == "active")
                {
                    query = query.Where(a => !a.IsResolved && !a.IsIgnored);
                }
            }

            // Filtrování podle časového období
            var fromDate = DateTime.Now.AddDays(-days);
            query = query.Where(a => a.Timestamp >= fromDate);

            // Filtrování podle zákazníka
            if (customerId.HasValue)
            {
                query = query.Where(a => a.Instance != null && a.Instance.CustomerId == customerId);
            }

            // Filtrování podle instance
            if (instanceId.HasValue)
            {
                query = query.Where(a => a.InstanceId == instanceId);
            }

            // Načtení souvisejících dat
            query = query.Include(a => a.Instance)
                         .ThenInclude(i => i != null ? i.Customer : null)
                         .Include(a => a.AlertRule);

            // Seřazení podle času (nejnovější první)
            query = query.OrderByDescending(a => a.Timestamp);

            var alerts = await query.ToListAsync();

            // Mapování na DTO
            var result = alerts.Select(a => new
            {
                id = a.Id,
                timestamp = a.Timestamp.ToLocalTime(), // Převod na lokální čas
                metricType = a.AlertRule?.MetricType, // Typ metriky z pravidla
                alertRuleName = a.AlertRule?.Name, // Název pravidla
                message = a.Description,
                description = a.Description, // Pro kompatibilitu s ResolveAlertModalComponent
                alertType = a.AlertRule?.MetricType ?? "system", // Pro ResolveAlertModalComponent - fallback pro systémové alerty
                severity = a.Severity,
                status = a.IsResolved ? "resolved" : (a.IsIgnored ? "ignored" : "active"),
                instanceId = a.InstanceId,
                instanceName = a.Instance?.Name,
                customerId = a.Instance?.CustomerId,
                customerName = a.Instance?.Customer?.Name,
                customerAbbreviation = a.Instance?.Customer?.Abbreviation,
                condition = a.AlertRule?.Condition, // Podmínka z pravidla
                threshold = a.AlertRule?.Threshold, // Threshold z pravidla
                alertRuleId = a.AlertRuleId, // ID pravidla
                resolvedAt = a.ResolvedAt.HasValue ? a.ResolvedAt.Value.ToLocalTime() : (DateTime?)null, // Převod na lokální čas
                resolvedBy = a.ResolvedBy,
                isIgnored = a.IsIgnored,
                resolution = a.Resolution,
                isResolved = a.IsResolved
            }).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při načítání alertů");
            return StatusCode(500, "Interní chyba serveru");
        }
    }

    [HttpPost("{id}/resolve")]
    public async Task<IActionResult> ResolveAlert(int id, [FromBody] ResolveAlertRequest? request = null)
    {
        try
        {
            var alert = await _context.Alerts.FindAsync(id);
            if (alert == null)
            {
                return NotFound();
            }

            if (alert.IsResolved)
            {
                return BadRequest(new { message = "Alert is already resolved" });
            }

            var userId = HttpContext.Items["UserId"] as int?;
            var username = userId.HasValue
                ? (await _context.Users.FindAsync(userId))?.Username
                : User.Identity?.Name ?? "Unknown";

            alert.IsResolved = true;
            alert.ResolvedAt = DateTime.UtcNow;
            alert.ResolvedBy = username;
            alert.Resolution = request?.Resolution ?? string.Empty;

            await _context.SaveChangesAsync();

            return Ok(new { message = "Alert resolved successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při řešení alertu");
            return StatusCode(500, "Interní chyba serveru");
        }
    }

    [HttpPost("{id}/ignore")]
    public async Task<IActionResult> IgnoreAlert(int id)
    {
        try
        {
            var alert = await _context.Alerts.FindAsync(id);
            if (alert == null)
            {
                return NotFound();
            }

            if (alert.IsResolved)
            {
                return BadRequest(new { message = "Alert is already resolved" });
            }

            if (alert.IsIgnored)
            {
                return BadRequest(new { message = "Alert is already ignored" });
            }

            alert.IsIgnored = true;

            await _context.SaveChangesAsync();

            return Ok(new { message = "Alert ignored successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při ignorování alertu");
            return StatusCode(500, "Interní chyba serveru");
        }
    }

    [HttpGet("rules")]
    public async Task<IActionResult> GetAlertRules()
    {
        try
        {
            var rules = await _context.Set<AlertRule>()
                .Include(r => r.Customer)
                .Include(r => r.Instance)
                .ToListAsync();

            var result = rules.Select(r => new
            {
                id = r.Id,
                name = r.Name,
                description = r.Description,
                metricType = r.MetricType,
                condition = r.Condition,
                threshold = r.Threshold,
                severity = r.Severity,
                enabled = r.Enabled,
                notifyByEmail = r.NotifyByEmail,
                emailRecipients = r.EmailRecipients,
                customerId = r.CustomerId,
                customerName = r.Customer?.Name,
                instanceId = r.InstanceId,
                instanceName = r.Instance?.Name,
                createdAt = r.CreatedAt,
                lastTriggeredAt = r.LastTriggeredAt,
                triggerCount = r.TriggerCount
            }).ToList();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při načítání pravidel pro alerty");
            return StatusCode(500, "Interní chyba serveru");
        }
    }

    [HttpGet("rules/{id}")]
    public async Task<IActionResult> GetAlertRule(int id)
    {
        try
        {
            var rule = await _context.Set<AlertRule>()
                .Include(r => r.Customer)
                .Include(r => r.Instance)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (rule == null)
            {
                return NotFound();
            }

            var result = new
            {
                id = rule.Id,
                name = rule.Name,
                description = rule.Description,
                metricType = rule.MetricType,
                condition = rule.Condition,
                threshold = rule.Threshold,
                severity = rule.Severity,
                enabled = rule.Enabled,
                notifyByEmail = rule.NotifyByEmail,
                emailRecipients = rule.EmailRecipients,
                customerId = rule.CustomerId,
                customerName = rule.Customer?.Name,
                instanceId = rule.InstanceId,
                instanceName = rule.Instance?.Name,
                createdAt = rule.CreatedAt,
                lastTriggeredAt = rule.LastTriggeredAt,
                triggerCount = rule.TriggerCount
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při načítání pravidla pro alert");
            return StatusCode(500, "Interní chyba serveru");
        }
    }

    [HttpPost("rules")]
    public async Task<IActionResult> CreateAlertRule([FromBody] AlertRuleRequest request)
    {
        try
        {
            var rule = new AlertRule
            {
                Name = request.Name,
                Description = request.Description,
                MetricType = request.MetricType,
                Condition = request.Condition,
                Threshold = request.Threshold,
                Severity = request.Severity,
                Enabled = request.Enabled,
                NotifyByEmail = request.NotifyByEmail,
                EmailRecipients = request.EmailRecipients,
                CustomerId = request.CustomerId,
                InstanceId = request.InstanceId,
                CreatedAt = DateTime.Now
            };

            _context.Set<AlertRule>().Add(rule);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetAlertRules), new { id = rule.Id }, rule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při vytváření pravidla pro alert");
            return StatusCode(500, "Interní chyba serveru");
        }
    }

    [HttpPut("rules/{id}")]
    public async Task<IActionResult> UpdateAlertRule(int id, [FromBody] AlertRuleRequest request)
    {
        try
        {
            var rule = await _context.Set<AlertRule>().FindAsync(id);
            if (rule == null)
            {
                return NotFound();
            }

            rule.Name = request.Name;
            rule.Description = request.Description;
            rule.MetricType = request.MetricType;
            rule.Condition = request.Condition;
            rule.Threshold = request.Threshold;
            rule.Severity = request.Severity;
            rule.Enabled = request.Enabled;
            rule.NotifyByEmail = request.NotifyByEmail;
            rule.EmailRecipients = request.EmailRecipients;
            rule.CustomerId = request.CustomerId;
            rule.InstanceId = request.InstanceId;

            await _context.SaveChangesAsync();

            return Ok(rule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při aktualizaci pravidla pro alert");
            return StatusCode(500, "Interní chyba serveru");
        }
    }

    [HttpDelete("rules/{id}")]
    public async Task<IActionResult> DeleteAlertRule(int id)
    {
        try
        {
            var rule = await _context.Set<AlertRule>().FindAsync(id);
            if (rule == null)
            {
                return NotFound();
            }

            _context.Set<AlertRule>().Remove(rule);
            await _context.SaveChangesAsync();

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při mazání pravidla pro alert");
            return StatusCode(500, "Interní chyba serveru");
        }
    }


}
